package com.cmg.common.utils;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2025/7/30 21:54
 */
public class RedisKeyUtil {

    public static final String SCOPE = "cmg-living";

    public static String getKey(BizTypeEnum bizTypeEnum, String... keys){
        StringBuilder sb  = new StringBuilder();
        sb.append(SCOPE).append(":").append(bizTypeEnum.getCode());
        for (String key : keys) {
            sb.append(":").append(key);
        }
        return sb.toString();
    }

    @AllArgsConstructor
    @Getter
    public static enum BizTypeEnum{

        CP("cp", "企业微信")

        ;

        public final String code;

        public final String desc;

    }

}

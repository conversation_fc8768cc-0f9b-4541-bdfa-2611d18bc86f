package com.cmg.common.core.domain.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/7/30 22:33
 */
@Data
@TableName("cmg_cp_user_info")
public class CpUserInfo {

    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 非企业用户身份标识
     */
    private String openId;

    private String deviceId;

    /**
     * 用户为企业成员时为userid
     */
    private String userId;

    /**
     * 成员姓名，2022年6月20号后的新应用将不再返回此字段，旧应用正常返回
     */
    private String name;

    /**
     * 成员手机号，仅在用户同意snsapi_privateinfo授权时返回
     */
    private String mobile;

    /**
     * 性别。0表示未定义，1表示男性，2表示女性
     */
    private String gender;

    /**
     * 成员邮箱，仅在用户同意snsapi_privateinfo授权时返回
     */
    private String email;

    /**
     * 头像url。注：如果要获取小图将url最后的”/0”改成”/100”即可。仅在用户同意snsapi_privateinfo授权时返回
     */
    private String avatar;

    /**
     * 员工个人二维码（扫描可添加为外部联系人），仅在用户同意snsapi_privateinfo授权时返回
     */
    private String qrCode;

    /**
     * 企业邮箱，仅在用户同意snsapi_privateinfo授权时返回，2022年6月20号后的新应用将返回
     */
    private String bizMail;

    /**
     * 地址，仅在用户同意snsapi_privateinfo授权时返回，2022年6月20号后的新应用将返回
     */
    private String address;

    @TableField(fill = FieldFill.INSERT)
    private Date createTime = new Date();

    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime = new Date();

}

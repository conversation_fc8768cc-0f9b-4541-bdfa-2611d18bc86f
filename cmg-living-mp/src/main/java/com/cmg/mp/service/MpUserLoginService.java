package com.cmg.mp.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.cmg.common.core.domain.R;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/30 22:09
 */
@Slf4j
@AllArgsConstructor
@Service
public class MpUserLoginService {

    private final WxMpService wxMpService;

    /**
     * 企业微信登录
     * @param code
     * @return
     * <AUTHOR>
     * @date 2025.07.30
     */
    public R login(String code) {
        try {
            WxOAuth2AccessToken wxOAuth2AccessToken = wxMpService.getOAuth2Service().getAccessToken(code);



            return R.ok();
        } catch (WxErrorException e) {
            log.error("微信公众号登录获取用户失败", e);
            return R.fail("获取用户信息失败");
        }
    }

}

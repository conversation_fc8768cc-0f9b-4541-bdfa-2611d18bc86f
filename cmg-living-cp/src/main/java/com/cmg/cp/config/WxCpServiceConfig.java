package com.cmg.cp.config;

import me.chanjar.weixin.cp.api.WxCpMenuService;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.api.WxCpService;
import me.chanjar.weixin.cp.api.impl.WxCpMenuServiceImpl;
import me.chanjar.weixin.cp.api.impl.WxCpOAuth2ServiceImpl;
import me.chanjar.weixin.cp.api.impl.WxCpServiceImpl;
import me.chanjar.weixin.cp.config.impl.WxCpRedisTemplateConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * <AUTHOR>
 * @date 2025/7/30 21:39
 */
@Configuration
public class WxCpServiceConfig {

    @Autowired
    private CorpInfoConfig corpInfoConfig;

    @Bean
    @ConditionalOnMissingBean
    public WxCpService wxCpService(StringRedisTemplate redisTemplate) {
        WxCpService wxCpService = new WxCpServiceImpl();
        WxCpRedisTemplateConfigImpl wxCpRedisTemplateConfig = new WxCpRedisTemplateConfigImpl(redisTemplate);
        //设置企业微信相关配置
        wxCpRedisTemplateConfig.setCorpId(corpInfoConfig.getCorpId());
        wxCpRedisTemplateConfig.setCorpSecret(corpInfoConfig.getCorpSecret());
        wxCpRedisTemplateConfig.setAgentId(corpInfoConfig.getAgentId());

        wxCpService.setWxCpConfigStorage(wxCpRedisTemplateConfig);
        return wxCpService;
    }

    @Bean
    @ConditionalOnMissingBean
    public WxCpOAuth2Service wxCpOAuth2Service(WxCpService wxCpService){
        WxCpOAuth2Service wxCpOAuth2Service = new WxCpOAuth2ServiceImpl(wxCpService);
        return wxCpOAuth2Service;
    }

    @Bean
    @ConditionalOnMissingBean
    public WxCpMenuService wxCpMenuService(WxCpService wxCpService){
        WxCpMenuService cpMenuService = new WxCpMenuServiceImpl(wxCpService);
        return cpMenuService;
    }

}

package com.cmg.cp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/7/30 21:52
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cmg.living.cp")
public class CorpInfoConfig {

    private String corpId;

    private String corpSecret;

    private Integer agentId;

}

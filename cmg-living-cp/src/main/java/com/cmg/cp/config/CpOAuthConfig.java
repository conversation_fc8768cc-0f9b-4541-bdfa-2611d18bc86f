package com.cmg.cp.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2025/8/2 11:38
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "cmg.living.o-auth")
public class CpOAuthConfig {

    private String redirectUri;

    private String state;

    private String scope;

}

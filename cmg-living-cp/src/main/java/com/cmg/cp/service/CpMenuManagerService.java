package com.cmg.cp.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Opt;
import com.cmg.cp.config.CpOAuthConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.menu.WxMenu;
import me.chanjar.weixin.common.bean.menu.WxMenuButton;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpMenuService;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/8/2 10:56
 */
@Slf4j
@AllArgsConstructor
@Service
public class CpMenuManagerService {

    private final WxCpMenuService wxCpMenuService;

    private final WxCpOAuth2Service wxCpOAuth2Service;

    private final CpOAuthConfig cpOAuthConfig;

    private static final List<String> menuList = new ArrayList<>();

    static {
        menuList.add("创建直播");
        menuList.add("客户列表");
        menuList.add("我的信息");
    }

    /**
     * 启动服务后初始化菜单
     * <AUTHOR>
     * @date 2025.08.02
     */
    @PostConstruct
    public void initMenu(){
        //校验菜单是否存在
//        Set<String> alreadyMenuList = new HashSet<>();
//        try {
//            List<WxMenuButton> buttons = new ArrayList<>();
//            WxMenu wxMenu = wxCpMenuService.get();
//            if(Objects.nonNull(wxMenu)){
//                buttons = Opt.ofNullable(wxMenu.getButtons()).orElse(new ArrayList<>());
//            }
//            for (WxMenuButton button : buttons) {
//                alreadyMenuList.add(button.getName());
//            }
//        } catch (WxErrorException e) {
//            log.error("获取企业微信菜单失败", e);
//        }
//
//        if(alreadyMenuList.containsAll(menuList)){
//            return;
//        }
//
//        //不存在则更新菜单
//        //找出不存在的菜单
//        List<String> needUpdateMenu = new ArrayList<>();
//        for (String menu : menuList) {
//            if(!alreadyMenuList.contains(menu)){
//                needUpdateMenu.add(menu);
//            }
//        }
//
//        if(CollectionUtil.isNotEmpty(needUpdateMenu)){
//            createMenu(needUpdateMenu);
//        }
    }


    public void createMenu(List<String> menuNameList){
        if(CollectionUtil.isEmpty(menuNameList)){
            return;
        }

        WxMenu menu = new WxMenu();
        menu.setButtons(
                menuNameList.stream().map(menuName -> {
                    WxMenuButton button = new WxMenuButton();
                    button.setType("click");
                    button.setName(menuName);
                    String redirectUrl = wxCpOAuth2Service.buildAuthorizationUrl(cpOAuthConfig.getRedirectUri(), cpOAuthConfig.getState(), cpOAuthConfig.getScope());
                    button.setKey(redirectUrl);
                    return button;
                }).collect(Collectors.toList())
        );

        try {
            wxCpMenuService.create(menu);
        } catch (WxErrorException e) {
            log.error("创建企业微信菜单失败", e);
        }
    }

}

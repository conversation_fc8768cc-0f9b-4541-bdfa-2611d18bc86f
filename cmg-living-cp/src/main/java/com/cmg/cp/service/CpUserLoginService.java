package com.cmg.cp.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.cmg.common.core.domain.R;
import com.cmg.common.core.domain.model.LoginUser;
import com.cmg.common.enums.BusinessAppTypeEnums;
import com.cmg.common.core.domain.entity.CpUserInfo;
import com.cmg.framework.web.service.TokenService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.cp.api.WxCpOAuth2Service;
import me.chanjar.weixin.cp.bean.WxCpOauth2UserInfo;
import me.chanjar.weixin.cp.bean.WxCpUserDetail;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2025/7/30 22:09
 */
@Slf4j
@AllArgsConstructor
@Service
public class CpUserLoginService {

    private final WxCpOAuth2Service wxCpOAuth2Service;

    private final CpUserManagerService cpUserManagerService;

    private final TokenService tokenService;

    /**
     * 企业微信登录
     * @param code
     * @return
     * <AUTHOR>
     * @date 2025.07.30
     */
    public R<String> login(String code) {
        try {
            WxCpOauth2UserInfo oauth2UserInfo = wxCpOAuth2Service.getAuthUserInfo(code);
            if(Objects.isNull(oauth2UserInfo)){
                return R.fail("获取用户信息失败");
            }
            CpUserInfo cpUserInfo = BeanUtil.toBean(oauth2UserInfo, CpUserInfo.class);
            WxCpUserDetail wxCpUserDetail = wxCpOAuth2Service.getUserDetail(oauth2UserInfo.getUserTicket());
            if(Objects.nonNull(wxCpUserDetail)){
                BeanUtil.copyProperties(wxCpUserDetail, cpUserInfo);
            }
            //根据userid判断是否存在
            cpUserManagerService.queryByUserId(cpUserInfo.getUserId()).ifPresentOrElse(
                    userInfo -> {
                        //更新用户信息
                        cpUserInfo.setId(userInfo.getId());
                        BeanUtil.copyProperties(cpUserInfo, userInfo, CopyOptions.create().ignoreNullValue());
                        cpUserManagerService.updateById(cpUserInfo);
                    },
                    () -> {
                        //新增用户信息
                        cpUserManagerService.save(cpUserInfo);
                    }
            );


            LoginUser loginUser = new LoginUser();
            loginUser.setAppType(BusinessAppTypeEnums.CP);
            loginUser.setUserId(Long.valueOf(cpUserInfo.getUserId()));
            loginUser.setCpUserInfo(cpUserInfo);

            String token = tokenService.createToken(loginUser);
            return R.ok(token);
        } catch (WxErrorException e) {
            log.error("企业微信登录获取用户失败", e);
            return R.fail("获取用户信息失败");
        }
    }

}

package com.cmg.cp.service;

import cn.hutool.core.lang.Opt;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.cmg.cp.mapper.CpUserInfoMapper;
import com.cmg.common.core.domain.entity.CpUserInfo;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2025/7/30 22:38
 */
@Service
public class CpUserManagerService extends ServiceImpl<CpUserInfoMapper, CpUserInfo> {
    public Opt<CpUserInfo> queryByUserId(String userId) {
        CpUserInfo cpUserInfo = this.lambdaQuery().eq(CpUserInfo::getUserId, userId).one();
        return Opt.of(cpUserInfo);
    }
}

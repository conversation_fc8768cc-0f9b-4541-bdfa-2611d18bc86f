package com.cmg.wechat.mp;

import com.cmg.common.annotation.Log;
import com.cmg.common.core.controller.BaseController;
import com.cmg.common.core.domain.AjaxResult;
import com.cmg.common.core.page.TableDataInfo;
import com.cmg.common.enums.BusinessType;
import com.cmg.mp.service.MpUserLoginService;
import com.cmg.system.domain.SysNotice;
import com.cmg.system.service.ISysNoticeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 公告 信息操作处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/mp/login")
public class LoginController extends BaseController {
    @Autowired
    private MpUserLoginService mpUserLoginService;

    @PostMapping
    public AjaxResult add() {
        return AjaxResult.success(mpUserLoginService.login("123"));
    }

    @GetMapping(value = "/fromWechat")
    public AjaxResult fromWechat(String code, String state) {
        Map map = new HashMap<>();
        map.put("code", code);
        map.put("state", state);
        return AjaxResult.success(map);
    }
}
